hub_communities <- membership(network_result$communities)[is_hub]
hub_degrees <- network_result$node_degrees[is_hub]
hub_betweenness <- network_result$betweenness[is_hub]
# Create a summary table
hub_data <- data.frame(
Family = hub_families,
Community = hub_communities,
Degree = hub_degrees,
Betweenness = hub_betweenness,
stringsAsFactors = FALSE
)
# Sort by degree (descending)
hub_data <- hub_data[order(hub_data$Degree, decreasing = TRUE), ]
# Limit families to avoid overcrowding
if (nrow(hub_data) <= 12) {
families_to_show <- hub_data
} else {
families_to_show <- hub_data[1:12, ]
}
# Create color mapping for communities
community_colors_hub <- community_colors[families_to_show$Community]
# Create bar plot for degree
bp <- barplot(families_to_show$Degree,
names.arg = families_to_show$Family,
col = adjustcolor(community_colors_hub, alpha.f = 0.8),
las = 2,
cex.names = 0.6,
main = "Hub Families: Degree (bars) & Betweenness (line)",
ylab = "Degree",
cex.main = 0.9,
ylim = c(0, max(families_to_show$Degree) * 1.1))
# Add betweenness as a line plot on secondary y-axis
par(new = TRUE)
# Scale betweenness to fit nicely with degree scale
betweenness_scaled <- families_to_show$Betweenness
if (max(betweenness_scaled) > 0) {
betweenness_scaled <- (betweenness_scaled / max(betweenness_scaled)) * max(families_to_show$Degree) * 0.8
}
plot(bp, betweenness_scaled,
type = "o",
pch = 16,
col = "red",
lwd = 2,
cex = 1.2,
axes = FALSE,
xlab = "",
ylab = "")
# Add right y-axis for betweenness
axis(4,
at = seq(0, max(families_to_show$Degree) * 0.8, length.out = 5),
labels = round(seq(0, max(families_to_show$Betweenness), length.out = 5), 1),
col = "red",
col.axis = "red")
mtext("Betweenness", side = 4, line = 2.5, col = "red", cex = 0.8)
# Add legend
legend("topright",
legend = c("Degree (bars)", "Betweenness (line)"),
fill = c("gray", NA),
border = c("black", NA),
lty = c(NA, 1),
pch = c(NA, 16),
col = c("black", "red"),
cex = 0.7,
bg = "white")
} else {
# If no hub nodes, show a message
plot.new()
text(0.5, 0.5, "No hub nodes identified\n(top 5% threshold)",
cex = 1.2, adj = 0.5)
}
# Add improved legend with better positioning
legend("bottomright",
legend = c("Positive correlation", "Negative correlation", "Hub nodes (top 5%)", "Communities"),
col = c("#2E8B57", "#DC143C", "black", "gray"),
lty = c(1, 1, NA, NA),
pch = c(NA, NA, 16, 15),
cex = 0.9,
bg = "white",
box.lty = 1)
# Reset layout
layout(1)
dev.off()
cat("Network plot saved:", filename, "\n")
cat("Hub nodes identified:", sum(is_hub), "out of", vcount(network), "total nodes\n")
}, error = function(e) {
cat("Error creating network plot:", e$message, "\n")
})
}
# Function to plot cross-correlation networks with improved visualization
plot_cross_network <- function(cross_network_result, group1_name, group2_name, taxonomic_level) {
if (is.null(cross_network_result)) {
cat("Cannot plot cross-network: network result is NULL\n")
return(NULL)
}
tryCatch({
network <- cross_network_result$network
# Check if network has vertices
if (vcount(network) == 0) {
cat("Network has no vertices, cannot plot\n")
return(NULL)
}
# Try different layouts for better visualization
# First try bipartite layout, if it fails use force-directed
layout <- tryCatch({
layout_as_bipartite(network)
}, error = function(e) {
cat("Bipartite layout failed, using force-directed layout\n")
layout_with_fr(network, niter = 1000)
})
# If layout is still problematic, use a simple layout
if (is.null(layout) || any(is.na(layout))) {
cat("Using circle layout as fallback\n")
layout <- layout_in_circle(network)
}
# Color nodes by group with muted, transparent colors (as you preferred)
base_group1_color <- "#87CEEB"  # Muted sky blue for bacteria
base_group2_color <- "#DDA0DD"  # Muted plum/lavender for fungi (distinct from green edges)
base_node_colors <- ifelse(V(network)$type == cross_network_result$group1_name, base_group1_color, base_group2_color)
node_colors <- adjustcolor(base_node_colors, alpha.f = 0.8)  # 80% opacity
# Scale down node sizes significantly
max_degree <- max(cross_network_result$node_degrees)
min_degree <- min(cross_network_result$node_degrees)
# Scale nodes between 3 and 10 (smaller than before)
node_sizes <- 3 + (cross_network_result$node_degrees - min_degree) / (max_degree - min_degree) * 7
# Edge visualization - different colors for positive and negative correlations
edge_colors <- ifelse(E(network)$sign == "positive", "#228B22", "#DC143C")  # Green for positive, red for negative
# Scale edge widths using absolute correlation values
edge_widths <- 0.8 + abs(E(network)$correlation) * 2.5
# More selective hub node identification (top 5%)
hub_threshold <- quantile(cross_network_result$node_degrees, 0.95)
is_hub <- cross_network_result$node_degrees >= hub_threshold
# Create filename
filename <- paste0(group1_name, "_vs_", group2_name, "_", taxonomic_level, "_cross_network.pdf")
# Create plot with higher resolution
pdf(filename, width = 16, height = 12)
# Create layout for main plot and subplot
layout(matrix(c(1, 1, 1, 2), nrow = 2, ncol = 2, byrow = TRUE), heights = c(3, 1))
# Count positive and negative edges
positive_edges <- sum(E(network)$sign == "positive")
negative_edges <- sum(E(network)$sign == "negative")
# Main cross-network plot (no labels for cleaner visualization)
plot(network,
layout = layout,
vertex.color = node_colors,
vertex.size = node_sizes,
vertex.frame.color = "white",
vertex.frame.width = 1.5,
vertex.label = NA,  # Remove all labels for cleaner plot
edge.color = edge_colors,
edge.width = edge_widths,
edge.curved = 0.15,
main = paste(group1_name, "vs", group2_name, "Cross-Network at", taxonomic_level, "Level"),
sub = paste("Nodes:", vcount(network), "| Total Edges:", ecount(network),
"| Positive:", positive_edges, "| Negative:", negative_edges,
"| Communities:", cross_network_result$community_count,
"| Hub nodes (top 5%):", sum(is_hub)),
cex.main = 1.3,
cex.sub = 0.9)
# Create subplot showing hub families with degree (bars) and betweenness (lines) by group
par(mar = c(4, 4, 2, 4))  # Extra margin for second y-axis
if (sum(is_hub) > 0) {
hub_families <- V(network)$name[is_hub]
hub_communities <- membership(cross_network_result$communities)[is_hub]
hub_degrees <- cross_network_result$node_degrees[is_hub]
hub_betweenness <- cross_network_result$betweenness[is_hub]
hub_types <- V(network)$type[is_hub]
# Create a summary table
hub_data <- data.frame(
Family = hub_families,
Community = hub_communities,
Degree = hub_degrees,
Betweenness = hub_betweenness,
Type = hub_types,
stringsAsFactors = FALSE
)
# Sort by degree (descending)
hub_data <- hub_data[order(hub_data$Degree, decreasing = TRUE), ]
# Show top families (limit to avoid overcrowding)
if (nrow(hub_data) <= 10) {
families_to_show <- hub_data
} else {
families_to_show <- hub_data[1:10, ]
}
# Create colors based on group type (bacteria vs fungi)
type_colors <- ifelse(families_to_show$Type == cross_network_result$group1_name,
base_group1_color, base_group2_color)
type_colors <- adjustcolor(type_colors, alpha.f = 0.8)
# Create bar plot for degree
bp <- barplot(families_to_show$Degree,
names.arg = families_to_show$Family,
col = type_colors,
las = 2,
cex.names = 0.6,
main = "Hub Families: Degree (bars) & Betweenness (line)",
ylab = "Degree",
cex.main = 0.9,
ylim = c(0, max(families_to_show$Degree) * 1.1))
# Add betweenness as a line plot on secondary y-axis
par(new = TRUE)
# Scale betweenness to fit nicely with degree scale
betweenness_scaled <- families_to_show$Betweenness
if (max(betweenness_scaled) > 0) {
betweenness_scaled <- (betweenness_scaled / max(betweenness_scaled)) * max(families_to_show$Degree) * 0.8
}
plot(bp, betweenness_scaled,
type = "o",
pch = 16,
col = "red",
lwd = 2,
cex = 1.2,
axes = FALSE,
xlab = "",
ylab = "")
# Add right y-axis for betweenness
axis(4,
at = seq(0, max(families_to_show$Degree) * 0.8, length.out = 5),
labels = round(seq(0, max(families_to_show$Betweenness), length.out = 5), 1),
col = "red",
col.axis = "red")
mtext("Betweenness", side = 4, line = 2.5, col = "red", cex = 0.8)
# Add legend for both metrics and groups
legend("topright",
legend = c("Degree (bars)", "Betweenness (line)",
cross_network_result$group1_name, cross_network_result$group2_name),
fill = c("gray", NA, base_group1_color, base_group2_color),
border = c("black", NA, "black", "black"),
lty = c(NA, 1, NA, NA),
pch = c(NA, 16, NA, NA),
col = c("black", "red", "black", "black"),
cex = 0.6,
bg = "white")
} else {
# If no hub nodes, show a message
plot.new()
text(0.5, 0.5, "No hub nodes identified\n(top 5% threshold)",
cex = 1.2, adj = 0.5)
}
# Add improved legend with both positive and negative correlations
legend("bottomright",
legend = c(paste(group1_name, "nodes"), paste(group2_name, "nodes"),
"Positive correlation", "Negative correlation", "Hub nodes (top 5%)"),
col = c(base_group1_color, base_group2_color, "#228B22", "#DC143C", "black"),
pch = c(16, 16, NA, NA, 16),
lty = c(NA, NA, 1, 1, NA),
cex = 0.9,
bg = "white",
box.lty = 1)
# Reset layout
layout(1)
dev.off()
cat("Cross-network plot saved:", filename, "\n")
cat("Hub nodes identified:", sum(is_hub), "out of", vcount(network), "total nodes\n")
cat("Group 1 (", cross_network_result$group1_name, ") nodes:", sum(V(network)$type == cross_network_result$group1_name), "\n")
cat("Group 2 (", cross_network_result$group2_name, ") nodes:", sum(V(network)$type == cross_network_result$group2_name), "\n")
}, error = function(e) {
cat("Error creating cross-network plot:", e$message, "\n")
cat("Error details:", e$message, "\n")
})
}
# Function to save hub node analysis
save_hub_node_analysis <- function(network_result, group_name, taxonomic_level) {
if (is.null(network_result)) {
return(NULL)
}
tryCatch({
# Create hub node data frame
hub_data <- data.frame(
Taxon = network_result$hub_nodes,
Degree = network_result$node_degrees[network_result$hub_nodes],
Betweenness = network_result$betweenness[network_result$hub_nodes],
Closeness = network_result$closeness[network_result$hub_nodes],
Community = membership(network_result$communities)[network_result$hub_nodes],
stringsAsFactors = FALSE
)
# Sort by degree
hub_data <- hub_data[order(hub_data$Degree, decreasing = TRUE), ]
# Save to CSV
filename <- paste0(group_name, "_", taxonomic_level, "_hub_nodes.csv")
write.csv(hub_data, filename, row.names = FALSE)
cat("Hub node analysis saved:", filename, "\n")
return(hub_data)
}, error = function(e) {
cat("Error saving hub node analysis:", e$message, "\n")
return(NULL)
})
}
# Function to save community analysis
save_community_analysis <- function(network_result, group_name, taxonomic_level) {
if (is.null(network_result)) {
return(NULL)
}
tryCatch({
# Create community data frame
community_data <- data.frame(
Taxon = network_result$taxa_names,
Community = membership(network_result$communities),
Degree = network_result$node_degrees,
Betweenness = network_result$betweenness,
Closeness = network_result$closeness,
stringsAsFactors = FALSE
)
# Sort by community and degree
community_data <- community_data[order(community_data$Community, community_data$Degree, decreasing = c(FALSE, TRUE)), ]
# Save to CSV
filename <- paste0(group_name, "_", taxonomic_level, "_communities.csv")
write.csv(community_data, filename, row.names = FALSE)
cat("Community analysis saved:", filename, "\n")
return(community_data)
}, error = function(e) {
cat("Error saving community analysis:", e$message, "\n")
return(NULL)
})
}
# Function to create network comparison table
create_network_comparison_table <- function(network_results_list, taxonomic_level) {
tryCatch({
comparison_data <- data.frame(
Network = character(),
Nodes = numeric(),
Edges = numeric(),
Avg_Degree = numeric(),
Max_Degree = numeric(),
Communities = numeric(),
Modularity = numeric(),
Hub_Nodes = numeric(),
Density = numeric(),
stringsAsFactors = FALSE
)
for (name in names(network_results_list)) {
result <- network_results_list[[name]]
if (!is.null(result)) {
comparison_data <- rbind(comparison_data, data.frame(
Network = name,
Nodes = vcount(result$network),
Edges = ecount(result$network),
Avg_Degree = mean(result$node_degrees),
Max_Degree = max(result$node_degrees),
Communities = result$community_count,
Modularity = result$modularity,
Hub_Nodes = length(result$hub_nodes),
Density = edge_density(result$network),
stringsAsFactors = FALSE
))
}
}
# Save comparison table
filename <- paste0("Network_Comparison_", taxonomic_level, "_Level.csv")
write.csv(comparison_data, filename, row.names = FALSE)
cat("Network comparison table saved:", filename, "\n")
return(comparison_data)
}, error = function(e) {
cat("Error creating comparison table:", e$message, "\n")
return(NULL)
})
}
# Function to create statistical comparison plots
create_statistical_plots <- function(individual_networks, cross_networks, taxonomic_level) {
tryCatch({
# Prepare data for individual networks
if (length(individual_networks) > 0) {
individual_stats <- data.frame(
Network = names(individual_networks),
Nodes = sapply(individual_networks, function(x) vcount(x$network)),
Edges = sapply(individual_networks, function(x) ecount(x$network)),
Avg_Degree = sapply(individual_networks, function(x) mean(x$node_degrees)),
Max_Degree = sapply(individual_networks, function(x) max(x$node_degrees)),
Communities = sapply(individual_networks, function(x) x$community_count),
Modularity = sapply(individual_networks, function(x) x$modularity),
Density = sapply(individual_networks, function(x) edge_density(x$network)),
stringsAsFactors = FALSE
)
# Add grouping variables
individual_stats$Kingdom <- ifelse(grepl("Bacteria", individual_stats$Network), "Bacteria", "Fungi")
individual_stats$Protection <- ifelse(grepl("Low", individual_stats$Network), "Low", "High")
# Create statistical comparison plots with better formatting
pdf(paste0("Network_Statistics_", taxonomic_level, "_Level.pdf"), width = 18, height = 14)
# Set up multi-panel plot with better margins
par(mfrow = c(3, 3), mar = c(5, 4, 3, 2), oma = c(2, 2, 2, 2))
# Plot 1: Number of nodes by kingdom and protection
boxplot(Nodes ~ Kingdom + Protection, data = individual_stats,
main = "Number of Nodes", xlab = "Kingdom + Protection", ylab = "Nodes",
col = adjustcolor(c("#87CEEB", "#98D982", "#87CEEB", "#98D982"), alpha.f = 0.8), las = 2)
# Plot 2: Number of edges by kingdom and protection
boxplot(Edges ~ Kingdom + Protection, data = individual_stats,
main = "Number of Edges", xlab = "Kingdom + Protection", ylab = "Edges",
col = adjustcolor(c("#87CEEB", "#98D982", "#87CEEB", "#98D982"), alpha.f = 0.8), las = 2)
# Plot 3: Average degree by kingdom and protection
boxplot(Avg_Degree ~ Kingdom + Protection, data = individual_stats,
main = "Average Degree", xlab = "Kingdom + Protection", ylab = "Average Degree",
col = adjustcolor(c("#87CEEB", "#98D982", "#87CEEB", "#98D982"), alpha.f = 0.8), las = 2)
# Plot 4: Communities by kingdom and protection
boxplot(Communities ~ Kingdom + Protection, data = individual_stats,
main = "Number of Communities", xlab = "Kingdom + Protection", ylab = "Communities",
col = adjustcolor(c("#87CEEB", "#98D982", "#87CEEB", "#98D982"), alpha.f = 0.8), las = 2)
# Plot 5: Modularity by kingdom and environment
boxplot(Modularity ~ Kingdom + Environment, data = individual_stats,
main = "Modularity", xlab = "Kingdom + Environment", ylab = "Modularity",
col = adjustcolor(c("#87CEEB", "#98D982", "#87CEEB", "#98D982"), alpha.f = 0.8), las = 2)
# Plot 6: Network density by kingdom and environment
boxplot(Density ~ Kingdom + Environment, data = individual_stats,
main = "Network Density", xlab = "Kingdom + Environment", ylab = "Density",
col = adjustcolor(c("#87CEEB", "#98D982", "#87CEEB", "#98D982"), alpha.f = 0.8), las = 2)
# Plot 7: Nodes comparison Old vs New
boxplot(Nodes ~ Time, data = individual_stats,
main = "Nodes: Old vs New", xlab = "Time", ylab = "Nodes",
col = adjustcolor(c("#FFB6C1", "#98FB98"), alpha.f = 0.8))
# Plot 8: Modularity comparison Old vs New
boxplot(Modularity ~ Time, data = individual_stats,
main = "Modularity: Old vs New", xlab = "Time", ylab = "Modularity",
col = adjustcolor(c("#FFB6C1", "#98FB98"), alpha.f = 0.8))
# Plot 9: Summary barplot
barplot(individual_stats$Nodes, names.arg = individual_stats$Network,
main = "Network Sizes Overview", xlab = "Networks", ylab = "Number of Nodes",
col = adjustcolor(rainbow(nrow(individual_stats)), alpha.f = 0.7),
las = 2, cex.names = 0.7)
dev.off()
cat("Statistical comparison plots saved: Network_Statistics_", taxonomic_level, "_Level.pdf\n")
}
# Create cross-network statistics if available
if (length(cross_networks) > 0) {
cross_stats <- data.frame(
Network = names(cross_networks),
Nodes = sapply(cross_networks, function(x) vcount(x$network)),
Edges = sapply(cross_networks, function(x) ecount(x$network)),
Avg_Degree = sapply(cross_networks, function(x) mean(x$node_degrees)),
Communities = sapply(cross_networks, function(x) x$community_count),
Modularity = sapply(cross_networks, function(x) x$modularity),
stringsAsFactors = FALSE
)
# Create cross-network comparison plot
pdf(paste0("Cross_Network_Statistics_", taxonomic_level, "_Level.pdf"), width = 12, height = 8)
par(mfrow = c(2, 3), mar = c(4, 4, 3, 2))
barplot(cross_stats$Nodes, names.arg = cross_stats$Network,
main = "Cross-Network Nodes", ylab = "Nodes",
col = adjustcolor("#DDA0DD", alpha.f = 0.8), las = 2, cex.names = 0.8)
barplot(cross_stats$Edges, names.arg = cross_stats$Network,
main = "Cross-Network Edges", ylab = "Edges",
col = adjustcolor("#DDA0DD", alpha.f = 0.8), las = 2, cex.names = 0.8)
barplot(cross_stats$Avg_Degree, names.arg = cross_stats$Network,
main = "Cross-Network Average Degree", ylab = "Average Degree",
col = adjustcolor("#DDA0DD", alpha.f = 0.8), las = 2, cex.names = 0.8)
barplot(cross_stats$Communities, names.arg = cross_stats$Network,
main = "Cross-Network Communities", ylab = "Communities",
col = adjustcolor("#DDA0DD", alpha.f = 0.8), las = 2, cex.names = 0.8)
barplot(cross_stats$Modularity, names.arg = cross_stats$Network,
main = "Cross-Network Modularity", ylab = "Modularity",
col = adjustcolor("#DDA0DD", alpha.f = 0.8), las = 2, cex.names = 0.8)
# Summary plot
plot(cross_stats$Edges, cross_stats$Modularity,
pch = 16, col = adjustcolor("#DDA0DD", alpha.f = 0.8), cex = 2,
main = "Cross-Network: Edges vs Modularity",
xlab = "Number of Edges", ylab = "Modularity")
text(cross_stats$Edges, cross_stats$Modularity,
labels = cross_stats$Network, pos = 3, cex = 0.7)
dev.off()
cat("Cross-network statistics saved: Cross_Network_Statistics_", taxonomic_level, "_Level.pdf\n")
}
}, error = function(e) {
cat("Error creating statistical plots:", e$message, "\n")
})
}
# Generate all plots and outputs
cat("\n\n========== GENERATING PLOTS AND OUTPUTS ==========\n")
# Plot individual networks and save analyses
individual_networks <- list()
if (exists("bacteria_low_network") && !is.null(bacteria_low_network)) {
plot_individual_network(bacteria_low_network, "Bacteria_Low_Protection", TAXONOMIC_LEVEL)
save_hub_node_analysis(bacteria_low_network, "Bacteria_Low_Protection", TAXONOMIC_LEVEL)
save_community_analysis(bacteria_low_network, "Bacteria_Low_Protection", TAXONOMIC_LEVEL)
individual_networks[["Bacteria_Low_Protection"]] <- bacteria_low_network
}
if (exists("bacteria_high_network") && !is.null(bacteria_high_network)) {
plot_individual_network(bacteria_high_network, "Bacteria_High_Protection", TAXONOMIC_LEVEL)
save_hub_node_analysis(bacteria_high_network, "Bacteria_High_Protection", TAXONOMIC_LEVEL)
save_community_analysis(bacteria_high_network, "Bacteria_High_Protection", TAXONOMIC_LEVEL)
individual_networks[["Bacteria_High_Protection"]] <- bacteria_high_network
}
if (exists("fungi_low_network") && !is.null(fungi_low_network)) {
plot_individual_network(fungi_low_network, "Fungi_Low_Protection", TAXONOMIC_LEVEL)
save_hub_node_analysis(fungi_low_network, "Fungi_Low_Protection", TAXONOMIC_LEVEL)
save_community_analysis(fungi_low_network, "Fungi_Low_Protection", TAXONOMIC_LEVEL)
individual_networks[["Fungi_Low_Protection"]] <- fungi_low_network
}
if (exists("fungi_high_network") && !is.null(fungi_high_network)) {
plot_individual_network(fungi_high_network, "Fungi_High_Protection", TAXONOMIC_LEVEL)
save_hub_node_analysis(fungi_high_network, "Fungi_High_Protection", TAXONOMIC_LEVEL)
save_community_analysis(fungi_high_network, "Fungi_High_Protection", TAXONOMIC_LEVEL)
individual_networks[["Fungi_High_Protection"]] <- fungi_high_network
}
# Plot cross-correlation networks
cross_networks <- list()
if (exists("bacteria_fungi_low_network") && !is.null(bacteria_fungi_low_network)) {
plot_cross_network(bacteria_fungi_low_network, "Bacteria_Low_Protection", "Fungi_Low_Protection", TAXONOMIC_LEVEL)
save_hub_node_analysis(bacteria_fungi_low_network, "Bacteria_Fungi_Low_Protection", TAXONOMIC_LEVEL)
cross_networks[["Bacteria_Fungi_Low_Protection"]] <- bacteria_fungi_low_network
}
if (exists("bacteria_fungi_high_network") && !is.null(bacteria_fungi_high_network)) {
plot_cross_network(bacteria_fungi_high_network, "Bacteria_High_Protection", "Fungi_High_Protection", TAXONOMIC_LEVEL)
save_hub_node_analysis(bacteria_fungi_high_network, "Bacteria_Fungi_High_Protection", TAXONOMIC_LEVEL)
cross_networks[["Bacteria_Fungi_High_Protection"]] <- bacteria_fungi_high_network
}
# Create comparison tables
if (length(individual_networks) > 0) {
individual_comparison <- create_network_comparison_table(individual_networks, paste0(TAXONOMIC_LEVEL, "_Individual"))
}
if (length(cross_networks) > 0) {
cross_comparison <- create_network_comparison_table(cross_networks, paste0(TAXONOMIC_LEVEL, "_Cross"))
}
# Create statistical plots
cat("\n========== CREATING STATISTICAL PLOTS ==========\n")
create_statistical_plots(individual_networks, cross_networks, TAXONOMIC_LEVEL)
cat("\n\n========== ANALYSIS COMPLETED ==========\n")
